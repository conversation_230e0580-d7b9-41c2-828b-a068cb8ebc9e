import { onMounted, onUnmounted } from "vue";
import { useGlobalProgress } from "./useGlobalProgress";

/**
 * 压缩进度监听 Composable
 * 监听主进程的压缩任务事件，并同步到全局进度管理系统
 */
export function useArchiveProgress() {
  const { addArchiveTask, updateArchiveTaskProgress, completeArchiveTask, errorArchiveTask } = useGlobalProgress();

  let isListenersSetup = false;

  /**
   * 获取 Electron API
   */
  const getElectronAPI = () => (window as any).electronAPI;

  /**
   * 设置压缩事件监听器
   */
  const setupArchiveEventListeners = () => {
    if (isListenersSetup) {
      console.log("📦 压缩事件监听器已设置，跳过重复设置");
      return;
    }

    try {
      const api = getElectronAPI();

      if (!api || !api.archive) {
        console.warn("📦 Electron Archive API 不可用");
        return;
      }

      // 监听压缩任务创建事件
      api.archive.onTaskCreated((taskId: string, task: any) => {
        console.log(`📦 压缩任务创建: ${task.name} (ID: ${taskId})`, task);

        // 创建前端进度任务
        const fileName = task.name ? `${task.name}.7z` : `archive_${Date.now()}.7z`;
        const totalFiles = task.totalFiles || 0;

        console.log(`📦 创建前端进度任务: ${fileName}, 总文件数: ${totalFiles}`);
        addArchiveTask(taskId, fileName, totalFiles);
      });

      // 监听压缩任务进度事件
      api.archive.onTaskProgress((taskId: string, progress: number, currentFile?: string) => {
        console.log(`📦 压缩进度更新: ${taskId} -> ${progress}% (${currentFile || ""})`);

        // 更新前端进度
        updateArchiveTaskProgress(taskId, progress, currentFile);
      });

      // 监听压缩任务完成事件
      api.archive.onTaskCompleted((taskId: string, result: any) => {
        console.log(`📦 压缩任务完成: ${taskId}`, result);

        // 完成前端任务
        completeArchiveTask(taskId, result?.archivePath);
      });

      // 监听压缩任务错误事件
      api.archive.onTaskError((taskId: string, error: string) => {
        console.error(`📦 压缩任务出错: ${taskId} - ${error}`);

        // 标记前端任务为错误状态
        errorArchiveTask(taskId, error);
      });

      // 监听压缩任务取消事件
      api.archive.onTaskCancelled((taskId: string) => {
        console.log(`📦 压缩任务取消: ${taskId}`);

        // 标记前端任务为取消状态
        errorArchiveTask(taskId, "用户取消");
      });

      isListenersSetup = true;
      console.log("📦 压缩事件监听器设置完成");
    } catch (error) {
      console.error("📦 设置压缩事件监听器失败:", error);
    }
  };

  /**
   * 清理事件监听器
   */
  const cleanupArchiveEventListeners = () => {
    if (!isListenersSetup) {
      return;
    }

    try {
      const api = getElectronAPI();

      if (api && api.archive && api.archive.removeAllListeners) {
        api.archive.removeAllListeners();
        console.log("📦 清理压缩事件监听器");
      }

      isListenersSetup = false;
    } catch (error) {
      console.error("📦 清理压缩事件监听器失败:", error);
    }
  };

  // 组件挂载时设置监听器
  onMounted(() => {
    setupArchiveEventListeners();
  });

  // 组件卸载时清理监听器
  onUnmounted(() => {
    cleanupArchiveEventListeners();
  });

  return {
    setupArchiveEventListeners,
    cleanupArchiveEventListeners,
    isListenersSetup: () => isListenersSetup,
  };
}
