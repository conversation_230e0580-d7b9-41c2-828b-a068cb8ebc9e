/**
 * 智能打包进度显示调试脚本
 * 用于测试和调试压缩进度显示功能
 */

// 模拟测试函数
export function testArchiveProgressDisplay() {
  console.log('🧪 开始测试智能打包进度显示功能');

  // 检查 Electron API 是否可用
  const api = (window as any).electronAPI;
  if (!api) {
    console.error('❌ Electron API 不可用');
    return;
  }

  if (!api.archive) {
    console.error('❌ Archive API 不可用');
    return;
  }

  console.log('✅ Electron Archive API 可用');

  // 检查事件监听器是否设置
  const hasEventListeners = !!(
    api.archive.onTaskCreated &&
    api.archive.onTaskProgress &&
    api.archive.onTaskCompleted &&
    api.archive.onTaskError &&
    api.archive.onTaskCancelled
  );

  if (hasEventListeners) {
    console.log('✅ Archive 事件监听器已设置');
  } else {
    console.error('❌ Archive 事件监听器未设置');
  }

  // 测试事件监听器
  console.log('🧪 测试事件监听器...');
  
  let testEventReceived = false;
  
  // 设置测试监听器
  api.archive.onTaskCreated((taskId: string, task: any) => {
    console.log('✅ 收到 task-created 事件:', taskId, task);
    testEventReceived = true;
  });

  api.archive.onTaskProgress((taskId: string, progress: number, currentFile?: string) => {
    console.log('✅ 收到 task-progress 事件:', taskId, progress, currentFile);
    testEventReceived = true;
  });

  api.archive.onTaskCompleted((taskId: string, result: any) => {
    console.log('✅ 收到 task-completed 事件:', taskId, result);
    testEventReceived = true;
  });

  api.archive.onTaskError((taskId: string, error: string) => {
    console.log('✅ 收到 task-error 事件:', taskId, error);
    testEventReceived = true;
  });

  // 检查全局进度管理器
  try {
    const { useGlobalProgress } = require('@/composables/useGlobalProgress');
    const globalProgress = useGlobalProgress();
    
    console.log('✅ 全局进度管理器可用');
    console.log('📊 当前任务数量:', globalProgress.tasks.value.length);
    console.log('📊 活跃任务数量:', globalProgress.activeTasks.value.length);
    console.log('📊 压缩任务数量:', globalProgress.archiveTasks.value.length);
    
  } catch (error) {
    console.error('❌ 全局进度管理器不可用:', error);
  }

  return {
    apiAvailable: !!api,
    archiveApiAvailable: !!api.archive,
    eventListenersAvailable: hasEventListeners,
    testEventReceived,
  };
}

/**
 * 调试智能打包流程
 */
export function debugSmartPackingFlow() {
  console.log('🔍 调试智能打包流程');

  const api = (window as any).electronAPI;
  if (!api || !api.tus) {
    console.error('❌ TUS API 不可用');
    return;
  }

  // 监听智能打包相关的日志
  console.log('🔍 监听智能打包流程...');
  
  // 检查智能打包阈值
  const threshold = parseInt(import.meta.env.VITE_SMART_PACKING_THRESHOLD) || 10;
  console.log(`📦 智能打包阈值: ${threshold} 个文件`);

  return {
    threshold,
    tusApiAvailable: !!api.tus,
  };
}

/**
 * 检查进度显示组件状态
 */
export function checkProgressComponents() {
  console.log('🔍 检查进度显示组件状态');

  // 查找进度显示相关的DOM元素
  const progressIndicator = document.querySelector('[data-testid="global-progress-indicator"]');
  const progressPanel = document.querySelector('[data-testid="progress-panel"]');
  const progressTasks = document.querySelectorAll('[data-testid="progress-task-item"]');

  console.log('📊 进度指示器:', progressIndicator ? '存在' : '不存在');
  console.log('📊 进度面板:', progressPanel ? '存在' : '不存在');
  console.log('📊 进度任务项数量:', progressTasks.length);

  // 检查是否有压缩任务
  const archiveTasks = Array.from(progressTasks).filter(task => {
    const taskType = task.getAttribute('data-task-type');
    return taskType === 'archive';
  });

  console.log('📦 压缩任务项数量:', archiveTasks.length);

  return {
    progressIndicatorExists: !!progressIndicator,
    progressPanelExists: !!progressPanel,
    totalTasks: progressTasks.length,
    archiveTasks: archiveTasks.length,
  };
}

/**
 * 完整的调试检查
 */
export function runFullDebugCheck() {
  console.log('🚀 运行完整的智能打包进度显示调试检查');
  
  const apiTest = testArchiveProgressDisplay();
  const flowTest = debugSmartPackingFlow();
  const componentTest = checkProgressComponents();

  const results = {
    timestamp: new Date().toISOString(),
    apiTest,
    flowTest,
    componentTest,
  };

  console.log('📋 调试检查结果:', results);

  // 生成调试报告
  const report = `
智能打包进度显示调试报告
========================
时间: ${results.timestamp}

API 测试:
- Electron API 可用: ${apiTest.apiAvailable ? '✅' : '❌'}
- Archive API 可用: ${apiTest.archiveApiAvailable ? '✅' : '❌'}
- 事件监听器可用: ${apiTest.eventListenersAvailable ? '✅' : '❌'}

流程测试:
- TUS API 可用: ${flowTest.tusApiAvailable ? '✅' : '❌'}
- 智能打包阈值: ${flowTest.threshold}

组件测试:
- 进度指示器存在: ${componentTest.progressIndicatorExists ? '✅' : '❌'}
- 进度面板存在: ${componentTest.progressPanelExists ? '✅' : '❌'}
- 总任务数: ${componentTest.totalTasks}
- 压缩任务数: ${componentTest.archiveTasks}

建议:
${!apiTest.apiAvailable ? '- 检查 Electron 环境是否正确初始化\n' : ''}
${!apiTest.archiveApiAvailable ? '- 检查 Archive 模块是否正确注册\n' : ''}
${!apiTest.eventListenersAvailable ? '- 检查事件监听器是否正确设置\n' : ''}
${!flowTest.tusApiAvailable ? '- 检查 TUS 模块是否正确初始化\n' : ''}
${!componentTest.progressIndicatorExists ? '- 检查 GlobalProgressIndicator 组件是否正确渲染\n' : ''}
`;

  console.log(report);
  return results;
}

// 在开发环境中自动运行调试检查
if (import.meta.env.DEV) {
  // 延迟执行，确保组件已加载
  setTimeout(() => {
    runFullDebugCheck();
  }, 2000);
}
