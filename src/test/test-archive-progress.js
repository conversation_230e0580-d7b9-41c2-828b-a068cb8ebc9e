/**
 * 测试智能打包进度显示功能
 * 在浏览器控制台中运行此脚本来测试功能
 */

// 测试函数：检查压缩进度显示
function testArchiveProgress() {
  console.log('🧪 开始测试智能打包进度显示功能');
  
  // 检查 API 可用性
  if (!window.electronAPI) {
    console.error('❌ Electron API 不可用');
    return false;
  }
  
  if (!window.electronAPI.archive) {
    console.error('❌ Archive API 不可用');
    return false;
  }
  
  console.log('✅ Archive API 可用');
  
  // 设置事件监听器来捕获压缩事件
  let eventsReceived = {
    taskCreated: false,
    taskProgress: false,
    taskCompleted: false
  };
  
  // 监听压缩任务创建
  window.electronAPI.archive.onTaskCreated((taskId, task) => {
    console.log('📦 收到压缩任务创建事件:', taskId, task);
    eventsReceived.taskCreated = true;
  });
  
  // 监听压缩进度更新
  window.electronAPI.archive.onTaskProgress((taskId, progress, currentFile) => {
    console.log(`📦 收到压缩进度事件: ${taskId} -> ${progress}% (${currentFile || ''})`);
    eventsReceived.taskProgress = true;
  });
  
  // 监听压缩完成
  window.electronAPI.archive.onTaskCompleted((taskId, result) => {
    console.log('📦 收到压缩完成事件:', taskId, result);
    eventsReceived.taskCompleted = true;
  });
  
  // 监听压缩错误
  window.electronAPI.archive.onTaskError((taskId, error) => {
    console.log('📦 收到压缩错误事件:', taskId, error);
  });
  
  console.log('✅ 事件监听器已设置');
  
  // 检查全局进度管理器
  const progressTasks = document.querySelectorAll('[data-testid="progress-task-item"]');
  console.log(`📊 当前进度任务数量: ${progressTasks.length}`);
  
  const archiveTasks = Array.from(progressTasks).filter(task => {
    return task.getAttribute('data-task-type') === 'archive';
  });
  console.log(`📦 当前压缩任务数量: ${archiveTasks.length}`);
  
  return {
    apiAvailable: true,
    eventsReceived,
    currentTasks: progressTasks.length,
    archiveTasks: archiveTasks.length
  };
}

// 检查进度显示组件
function checkProgressDisplay() {
  console.log('🔍 检查进度显示组件');
  
  const progressIndicator = document.querySelector('.global-progress-indicator');
  const progressPanel = document.querySelector('.progress-panel');
  const progressButton = document.querySelector('.progress-float-button');
  
  console.log('📊 进度指示器:', progressIndicator ? '存在' : '不存在');
  console.log('📊 进度面板:', progressPanel ? '存在' : '不存在');
  console.log('📊 进度按钮:', progressButton ? '存在' : '不存在');
  
  // 检查是否有活跃的任务
  const activeTasks = document.querySelectorAll('.progress-task-item');
  console.log(`📊 活跃任务数量: ${activeTasks.length}`);
  
  activeTasks.forEach((task, index) => {
    const taskType = task.querySelector('.task-type')?.textContent || '未知';
    const taskName = task.querySelector('.task-name')?.textContent || '未知';
    const taskProgress = task.querySelector('.task-progress')?.textContent || '未知';
    
    console.log(`📋 任务 ${index + 1}: ${taskType} - ${taskName} - ${taskProgress}`);
  });
  
  return {
    componentsFound: {
      indicator: !!progressIndicator,
      panel: !!progressPanel,
      button: !!progressButton
    },
    activeTasks: activeTasks.length
  };
}

// 模拟压缩任务（仅用于测试事件监听）
function simulateArchiveTask() {
  console.log('🧪 模拟压缩任务（测试事件监听）');
  
  // 这只是测试事件监听器，不会实际创建压缩任务
  const mockTaskId = 'test_archive_' + Date.now();
  const mockTask = {
    id: mockTaskId,
    name: 'test_archive',
    totalFiles: 100,
    progress: 0
  };
  
  console.log('📦 模拟任务创建事件');
  // 注意：这里不能直接触发事件，因为事件来自主进程
  // 这个函数主要用于验证事件监听器是否正确设置
  
  return mockTaskId;
}

// 运行完整测试
function runFullTest() {
  console.log('🚀 运行完整的智能打包进度显示测试');
  
  const apiTest = testArchiveProgress();
  const displayTest = checkProgressDisplay();
  
  const results = {
    timestamp: new Date().toISOString(),
    apiTest,
    displayTest,
    recommendations: []
  };
  
  // 生成建议
  if (!apiTest.apiAvailable) {
    results.recommendations.push('检查 Electron 环境是否正确初始化');
  }
  
  if (!displayTest.componentsFound.indicator) {
    results.recommendations.push('检查 GlobalProgressIndicator 组件是否正确渲染');
  }
  
  if (displayTest.activeTasks === 0) {
    results.recommendations.push('当前没有活跃任务，可以尝试上传文件来测试');
  }
  
  console.log('📋 测试结果:', results);
  
  // 生成测试报告
  const report = `
智能打包进度显示测试报告
========================
时间: ${results.timestamp}

API 测试结果:
- Archive API 可用: ${apiTest ? '✅' : '❌'}

显示组件测试结果:
- 进度指示器: ${displayTest.componentsFound.indicator ? '✅' : '❌'}
- 进度面板: ${displayTest.componentsFound.panel ? '✅' : '❌'}
- 进度按钮: ${displayTest.componentsFound.button ? '✅' : '❌'}
- 活跃任务数: ${displayTest.activeTasks}

建议:
${results.recommendations.map(r => `- ${r}`).join('\n')}

使用说明:
1. 选择 10+ 个文件进行上传以触发智能打包
2. 观察控制台是否有压缩事件日志
3. 检查进度面板是否显示压缩任务
4. 验证压缩进度是否实时更新
`;
  
  console.log(report);
  return results;
}

// 导出测试函数供控制台使用
window.testArchiveProgress = testArchiveProgress;
window.checkProgressDisplay = checkProgressDisplay;
window.runFullTest = runFullTest;

console.log('🧪 智能打包进度测试脚本已加载');
console.log('💡 在控制台中运行 runFullTest() 来执行完整测试');
