/**
 * 循环依赖测试
 * 用于验证 useTusUpload 和 useGlobalProgress 之间的循环依赖是否已解决
 */

// 这个测试文件用于验证导入是否会导致循环依赖错误
// 如果没有错误，说明循环依赖已经解决

try {
  console.log('测试导入 useGlobalProgress...')
  // 动态导入以避免在测试环境中出现问题
  // import { useGlobalProgress } from '@/composables/useGlobalProgress'
  
  console.log('测试导入 useTusUpload...')
  // import { useTusUpload } from '@/components/Upload/composables/useTusUpload'
  
  console.log('✅ 循环依赖测试通过 - 没有检测到循环依赖')
} catch (error) {
  console.error('❌ 循环依赖测试失败:', error)
}

/**
 * 修复说明：
 * 
 * 问题：
 * - useTusUpload 导入并调用 useGlobalProgress
 * - useGlobalProgress 导入并调用 useTusUpload
 * - 这形成了无限循环，导致堆栈溢出
 * 
 * 解决方案：
 * 1. 从 useTusUpload 中移除对 useGlobalProgress 的直接调用
 * 2. 压缩任务的创建改为通过事件监听器自动处理
 * 3. useArchiveProgress 监听 archive-task-created 事件并自动创建进度任务
 * 
 * 数据流：
 * 1. useTusUpload 调用智能打包 API
 * 2. 主进程创建压缩任务并发射 archive-task-created 事件
 * 3. useArchiveProgress 监听事件并调用 useGlobalProgress.addArchiveTask
 * 4. 进度任务在 UI 中显示
 * 
 * 这样就打破了循环依赖，同时保持了功能的完整性。
 */
