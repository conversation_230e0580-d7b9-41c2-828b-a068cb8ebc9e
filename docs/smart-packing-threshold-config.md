# 智能打包阈值配置化改造

## 概述

本次更新将智能打包功能的文件数量阈值从硬编码的50个文件调整为可配置的10个文件，并实现了完整的环境变量配置化改造。

## 配置变更

### 阈值调整
- **原阈值**: 50个文件
- **新阈值**: 10个文件
- **配置方式**: 环境变量 `VITE_SMART_PACKING_THRESHOLD`

### 环境变量配置

#### 新增环境变量
```bash
VITE_SMART_PACKING_THRESHOLD=10
```

#### 配置文件更新
以下环境变量文件已添加智能打包配置：
- `.env.development`
- `.env.production` 
- `.env.test`

## 代码修改详情

### 1. 环境变量类型定义

#### `src/types/env.d.ts`
```typescript
interface ImportMetaEnv {
  // 智能打包配置
  readonly VITE_SMART_PACKING_THRESHOLD: string;
  // ... 其他配置
}

export type AppConfig = {
  // ... 其他配置
  smartPacking: {
    threshold: number;
  };
  // ... 其他配置
};
```

### 2. 配置读取逻辑

#### `src/config/index.ts`
```typescript
export const config: AppConfig = {
  // ... 其他配置
  smartPacking: {
    threshold: toNumber(getEnvValue("VITE_SMART_PACKING_THRESHOLD"), 10), // 默认10个文件
  },
  // ... 其他配置
};
```

### 3. 前端代码更新

#### `src/components/Upload/composables/useTusUpload.ts`
- 导入配置: `import { config } from "@/config";`
- 使用配置: `const smartPackingThreshold = config.smartPacking.threshold;`
- 替换所有硬编码的50为 `smartPackingThreshold`

**修改的具体位置**:
1. 智能打包检测条件: `files.length >= smartPackingThreshold`
2. 日志输出中的阈值显示
3. API调用参数: `threshold: smartPackingThreshold`
4. 用户提示信息中的阈值显示

### 4. Electron后端代码更新

#### `electron/tus/uploadManager.ts`
```typescript
const threshold = options?.threshold || Number(process.env.VITE_SMART_PACKING_THRESHOLD) || 10;
```

#### `electron/tus/ipcHandlers.ts`
```typescript
threshold: options.threshold || Number(process.env.VITE_SMART_PACKING_THRESHOLD) || 10,
```

#### `electron/archive/archiveManager.ts`
```typescript
const threshold = options?.threshold || Number(process.env.VITE_SMART_PACKING_THRESHOLD) || 10;
```

### 5. 类型定义更新

#### `electron/archive/types.ts`
更新了 `BatchPackingOptions` 接口的注释，说明阈值的默认值来源。

## 向后兼容性

### 默认值策略
1. **环境变量优先**: 如果设置了 `VITE_SMART_PACKING_THRESHOLD`，使用该值
2. **代码默认值**: 如果环境变量未设置，使用默认值10
3. **API参数覆盖**: API调用时可以通过 `options.threshold` 参数覆盖默认值

### 兼容性保证
- 所有现有的API接口保持不变
- 支持运行时动态配置（通过API参数）
- 提供合理的默认值，确保功能正常工作

## 配置优先级

配置的优先级从高到低：
1. **API调用参数**: `options.threshold`
2. **环境变量**: `VITE_SMART_PACKING_THRESHOLD`
3. **代码默认值**: 10

## 影响范围

### 功能影响
- 智能打包现在在文件数量达到10个时触发（原来是50个）
- 用户将更频繁地看到智能打包功能
- 小批量文件上传也能享受打包优化

### 性能影响
- 更早触发智能打包，可能提高小批量文件的上传效率
- 减少HTTP请求数量，降低网络开销
- 对于10-50个文件的场景，用户体验得到改善

## 测试建议

### 功能测试
1. **阈值测试**:
   - 上传9个文件，验证不触发智能打包
   - 上传10个文件，验证触发智能打包
   - 上传大量文件，验证智能打包正常工作

2. **配置测试**:
   - 修改环境变量值，验证配置生效
   - 测试不同环境下的配置加载
   - 验证API参数覆盖功能

3. **兼容性测试**:
   - 测试环境变量未设置时的默认行为
   - 验证现有上传流程不受影响

### 性能测试
1. **小批量文件测试**: 测试10-20个文件的上传性能
2. **大批量文件测试**: 验证大量文件上传的稳定性
3. **网络效率测试**: 对比打包前后的网络请求数量

## 部署注意事项

### 环境变量配置
确保在部署时正确设置环境变量：
```bash
# 开发环境
VITE_SMART_PACKING_THRESHOLD=10

# 生产环境（可根据实际需求调整）
VITE_SMART_PACKING_THRESHOLD=10

# 测试环境
VITE_SMART_PACKING_THRESHOLD=5  # 可以设置更小的值用于测试
```

### 配置验证
部署后验证配置是否正确加载：
1. 检查前端配置对象中的值
2. 验证智能打包触发的文件数量
3. 确认日志中显示的阈值正确

## 未来扩展

### 可配置项扩展
未来可以考虑将更多智能打包相关的参数配置化：
- 压缩级别
- 最大文件大小限制
- 压缩格式选择
- 自动清理设置

### 动态配置
可以考虑实现运行时动态配置功能，允许用户在界面中调整智能打包阈值。
