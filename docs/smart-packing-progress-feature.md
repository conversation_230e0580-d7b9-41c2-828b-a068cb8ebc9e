# 智能打包进度显示功能

## 功能概述

智能打包进度显示功能为用户提供实时的压缩进度反馈，提升用户体验。当文件数量达到智能打包阈值（默认10个文件）时，系统会自动触发7z压缩，并在进度面板中显示压缩进度。

## 功能特性

### 🎯 核心功能
- **实时进度显示**：显示压缩进度百分比和当前处理文件
- **状态指示**：清晰的"正在智能打包..."状态提示
- **文件计数**：显示已处理文件数/总文件数
- **无缝切换**：压缩完成后自动切换到上传进度显示

### 🎨 UI 设计
- **专用图标**：使用归档图标区别于上传/下载任务
- **颜色区分**：蓝色进度条标识压缩任务
- **信息丰富**：显示当前处理文件名和进度详情
- **操作限制**：压缩任务不支持暂停/恢复操作

### ⚡ 性能优化
- **事件驱动**：基于IPC事件的实时进度更新
- **内存友好**：任务完成后自动清理，避免内存泄漏
- **响应迅速**：流畅的进度更新，无卡顿现象

## 技术架构

### 组件结构
```
GlobalProgressIndicator/
├── index.vue                 # 主容器组件
├── ProgressTaskItem.vue      # 任务项显示组件（已扩展支持压缩任务）
├── ProgressFloatButton.vue   # 悬浮按钮组件
└── ProgressPanel.vue         # 进度面板组件
```

### 核心 Composables
```
src/composables/
├── useGlobalProgress.ts       # 全局进度管理（已扩展压缩任务支持）
└── useArchiveProgress.ts      # 压缩进度监听（新增）
```

### 数据流
```
主进程 ArchiveManager
    ↓ (IPC Events)
渲染进程 useArchiveProgress
    ↓ (Method Calls)
useGlobalProgress
    ↓ (Reactive Data)
UI Components
```

## 使用方式

### 自动触发
当用户选择上传的文件数量 ≥ 智能打包阈值时，系统会自动：
1. 分析文件是否适合打包
2. 创建压缩任务
3. 显示压缩进度
4. 压缩完成后开始上传

### 进度信息
用户可以在进度面板中看到：
- 压缩包名称（通常为 `{文件夹名}.7z`）
- 实时进度百分比
- 当前正在处理的文件名
- 已处理文件数/总文件数

### 操作选项
- **取消**：可以随时取消压缩任务
- **查看历史**：压缩完成或失败的任务会出现在历史记录中

## 配置选项

### 环境变量
```bash
# 智能打包文件数量阈值（默认：10）
VITE_SMART_PACKING_THRESHOLD=10
```

### 运行时配置
智能打包阈值可以通过 API 参数动态覆盖：
```typescript
api.tus.smartPackUpload(filePaths, {
  threshold: 20, // 覆盖默认阈值
  archiveName: 'custom_name',
  metadata: { ... }
})
```

## 事件监听

### 主要事件
- `archive-task-created`：压缩任务创建
- `archive-task-progress`：压缩进度更新
- `archive-task-completed`：压缩任务完成
- `archive-task-error`：压缩任务出错
- `archive-task-cancelled`：压缩任务取消

### 事件处理
```typescript
// 自动设置事件监听器
useArchiveProgress() // 在 GlobalProgressIndicator 中自动调用
```

## 状态管理

### 任务状态
- `pending`：等待开始
- `in-progress`：正在压缩
- `completed`：压缩完成
- `error`：压缩出错
- `cancelled`：用户取消

### 数据结构
```typescript
interface ProgressTask {
  id: string
  type: "archive"
  fileName: string
  progress: number
  status: TaskStatus
  archiveTaskId: string
  currentFile?: string
  totalFiles?: number
  processedFiles?: number
  // ... 其他字段
}
```

## 错误处理

### 常见错误
- **磁盘空间不足**：显示具体错误信息
- **文件访问权限**：提示权限问题
- **7z程序异常**：显示压缩工具错误

### 错误恢复
- 错误任务自动移动到历史记录
- 用户可以查看详细错误信息
- 支持重新尝试上传（回退到常规上传）

## 性能考虑

### 内存管理
- 任务完成后自动清理进度数据
- 历史记录限制数量，避免无限增长
- 事件监听器正确清理，防止内存泄漏

### 用户体验
- 进度更新频率适中，避免过于频繁的UI刷新
- 大文件压缩时提供预估时间信息
- 支持后台压缩，不阻塞其他操作

## 兼容性

### 浏览器支持
- 仅在 Electron 环境中可用
- 依赖主进程的7z压缩功能

### 文件类型
- 支持所有文件类型的压缩
- 自动处理文件夹结构保持

## 调试信息

### 日志级别
开发环境下会输出详细的调试日志：
```
📦 压缩任务创建: folder_name.7z (ID: task_123)
📦 压缩进度更新: task_123 -> 45% (file.txt)
📦 压缩任务完成: task_123
```

### 故障排除
1. 检查控制台日志确认事件监听器是否正确设置
2. 验证智能打包阈值配置是否正确
3. 确认7z压缩工具是否可用

## 未来扩展

### 计划功能
- 压缩级别选择
- 压缩格式选择（7z/zip）
- 压缩预览和确认对话框
- 批量压缩任务管理

### 性能优化
- 多线程压缩支持
- 增量压缩功能
- 压缩缓存机制
