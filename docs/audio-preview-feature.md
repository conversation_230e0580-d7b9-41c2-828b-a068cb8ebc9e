# 音频预览功能文档

## 功能概述

音频预览功能扩展了现有的文件预览系统，为用户提供了完整的音频文件播放体验。该功能支持多种常见音频格式，并提供了直观的播放控件和键盘快捷键支持。

## 支持的音频格式

### 文件扩展名
- **MP3** - 最常见的音频格式
- **WAV** - 无损音频格式
- **FLAC** - 高质量无损压缩格式
- **AAC** - 高效音频编码格式
- **OGG** - 开源音频格式
- **M4A** - Apple音频格式
- **WMA** - Windows Media Audio格式
- **OPUS** - 现代低延迟音频格式

### MIME类型支持
- `audio/mpeg` (MP3)
- `audio/mp3`
- `audio/wav`
- `audio/flac`
- `audio/aac`
- `audio/ogg`
- `audio/mp4` (M4A)
- `audio/x-m4a`
- `audio/x-ms-wma`
- `audio/opus`

## 功能特性

### 基础播放控件
- **播放/暂停按钮** - 大型圆形主控制按钮
- **快进/快退按钮** - 支持10秒跳跃
- **进度条** - 可点击跳转到指定位置
- **时间显示** - 当前时间/总时长格式化显示
- **音量控制** - 音量滑块和静音按钮

### 键盘快捷键
- **空格键** - 播放/暂停切换
- **左箭头** - 快退5秒
- **右箭头** - 快进5秒
- **上箭头** - 音量增加10%
- **下箭头** - 音量减少10%
- **M键** - 静音/取消静音

### 视觉设计
- **音频图标** - 中央显示的音乐图标
- **状态指示器** - 播放/暂停/准备就绪状态显示
- **进度可视化** - 播放进度和缓冲进度显示
- **响应式布局** - 适配不同屏幕尺寸

### 错误处理
- **加载状态** - 显示加载动画
- **错误提示** - 友好的错误信息显示
- **重试功能** - 一键重新加载音频

## 技术架构

### 组件结构
```
src/components/Preview/
├── AudioPreview.vue              # 音频预览组件
├── composables/
│   ├── useAudioPlayer.ts         # 音频播放逻辑
│   └── useFilePreview.ts         # 文件预览逻辑（已扩展）
├── FilePreviewModal.vue          # 预览模态框（已更新）
└── index.ts                      # 导出文件（已更新）
```

### 核心Composable

#### useAudioPlayer
提供完整的音频播放控制功能：
- 播放状态管理
- 进度和时间控制
- 音量控制
- 键盘快捷键处理
- 错误处理

#### useFilePreview（扩展）
扩展了文件类型检测，新增音频格式支持：
- 音频文件扩展名识别
- 音频MIME类型检测
- 返回类型包含 `"audio"` 选项

## 使用方法

### 基础使用
音频预览功能已集成到现有的文件预览系统中，用户只需：

1. **双击音频文件** - 如果文件包含 `preview` 字段，将自动打开音频预览
2. **使用播放控件** - 点击播放按钮开始播放
3. **键盘控制** - 使用快捷键进行播放控制

### 开发者使用

#### 直接使用AudioPreview组件
```vue
<template>
  <AudioPreview 
    :audio-url="audioUrl"
    :file-name="fileName"
    :is-loading="isLoading"
    :error="error"
    @retry="handleRetry"
  />
</template>
```

#### 使用useAudioPlayer Composable
```typescript
import { useAudioPlayer } from '@/components/Preview'

const audioPlayer = useAudioPlayer({
  audioUrl: 'path/to/audio.mp3',
  autoPlay: false,
  volume: 0.8
})

// 播放控制
await audioPlayer.play()
audioPlayer.pause()
audioPlayer.togglePlay()

// 进度控制
audioPlayer.seek(30) // 跳转到30秒
audioPlayer.skipForward(10) // 快进10秒
audioPlayer.skipBackward(10) // 快退10秒

// 音量控制
audioPlayer.setVolume(0.5) // 设置音量为50%
audioPlayer.toggleMute() // 切换静音
```

## 集成说明

### 自动集成
音频预览功能已自动集成到现有的文件预览系统中：

1. **文件类型检测** - `useFilePreview` 自动识别音频文件
2. **预览模态框** - `FilePreviewModal` 自动显示音频预览组件
3. **双击触发** - 文件列表中的双击事件自动支持音频预览

### 预览条件
音频文件需要满足以下条件才能预览：
- 文件数据中包含 `preview` 字段且不为空
- 文件扩展名或MIME类型被识别为音频格式

## 性能优化

### 音频加载
- **预加载元数据** - 使用 `preload="metadata"` 快速获取时长信息
- **懒加载** - 只在用户打开预览时加载音频数据
- **错误恢复** - 自动处理加载失败并提供重试选项

### 内存管理
- **事件清理** - 组件卸载时自动清理事件监听器
- **状态重置** - URL变化时自动重置播放状态
- **资源释放** - 适当的资源清理机制

## 浏览器兼容性

### 支持的浏览器
- **Chrome/Chromium** - 完全支持所有功能
- **Firefox** - 完全支持所有功能
- **Safari** - 完全支持所有功能
- **Edge** - 完全支持所有功能

### 音频格式兼容性
不同浏览器对音频格式的支持可能有差异：
- **MP3** - 所有现代浏览器支持
- **WAV** - 所有现代浏览器支持
- **OGG** - Chrome、Firefox支持，Safari不支持
- **FLAC** - Chrome、Firefox支持，Safari部分支持
- **AAC/M4A** - 所有现代浏览器支持

## 故障排除

### 常见问题

1. **音频无法播放**
   - 检查文件格式是否受浏览器支持
   - 确认预览URL是否正确
   - 检查网络连接

2. **键盘快捷键不工作**
   - 确保没有输入框处于焦点状态
   - 检查是否有其他组件拦截键盘事件

3. **进度条不响应点击**
   - 确认音频已加载完元数据
   - 检查音频文件是否损坏

### 调试信息
开发模式下，音频播放器会在控制台输出详细的调试信息，包括：
- 播放状态变化
- 错误信息
- 事件触发情况

## 未来扩展

### 可能的增强功能
- **播放列表支持** - 支持多个音频文件连续播放
- **音频可视化** - 添加频谱分析显示
- **播放速度控制** - 支持变速播放
- **音频剪辑** - 支持音频片段选择和导出
- **歌词显示** - 支持LRC歌词文件显示

### 扩展接口
当前架构已为未来扩展预留了接口：
- `useAudioPlayer` 可轻松扩展新功能
- `AudioPreview` 组件支持插槽扩展
- 事件系统支持自定义监听器
