# TUS上传和下载分片大小调整为20MB

## 概述

本次更新将TUS上传和下载功能的分片大小从5MB调整为20MB，以提高大文件传输的效率。

## 修改内容

### 1. 环境变量配置文件

#### `.env.development`
- `VITE_TUS_CHUNK_SIZE`: 5242880 → 20971520 (20MB)
- 新增 `VITE_DOWNLOAD_CHUNK_SIZE`: 20971520 (20MB)

#### `.env.production`
- `VITE_TUS_CHUNK_SIZE`: 5242880 → 20971520 (20MB)
- 新增 `VITE_DOWNLOAD_CHUNK_SIZE`: 20971520 (20MB)

#### `.env.test`
- `VITE_TUS_CHUNK_SIZE`: 5242880 → 20971520 (20MB)
- 新增 `VITE_DOWNLOAD_CHUNK_SIZE`: 20971520 (20MB)

### 2. 前端配置文件

#### `src/config/index.ts`
- TUS配置默认分片大小: 5242880 → 20971520 (20MB)

### 3. Electron主进程配置

#### `electron/main.ts`
- TUS上传配置默认分片大小: 5MB → 20MB
- 下载配置默认分片大小: 5MB → 20MB
- 更新日志输出中的默认值

### 4. TUS上传模块

#### `electron/tus/index.ts`
- `createDefaultTusConfig`函数默认分片大小: 5MB → 20MB

#### `electron/tus/uploadManager.ts`
- 存储默认设置中的分片大小: 5MB → 20MB
- 上传选项中的默认分片大小: 5MB → 20MB

### 5. 下载模块

#### `electron/stream-downloader/downloadManager.ts`
- 存储默认设置中的分片大小: 5MB → 20MB
- 下载任务中的默认分片大小: 5MB → 20MB

#### `electron/stream-downloader/types.ts`
- 接口注释更新: 默认5MB → 默认20MB

### 6. 文档更新

#### `electron/tus/README.md`
- 示例代码中的分片大小: 5MB → 20MB
- 接口文档注释: 默认5MB → 默认20MB

## 技术细节

### 分片大小计算
- **5MB**: 5 * 1024 * 1024 = 5,242,880 bytes
- **20MB**: 20 * 1024 * 1024 = 20,971,520 bytes

### 影响范围
1. **上传功能**：
   - 普通文件上传
   - 拖拽上传
   - 批量上传
   - 文件夹上传
   - 智能打包上传

2. **下载功能**：
   - 单文件下载
   - 批量下载
   - 断点续传
   - 7z文件下载和解压

### 兼容性
- 所有现有的暂停/恢复功能保持不变
- 断点续传机制继续正常工作
- 任务队列管理不受影响

## 预期效果

### 优势
1. **提高传输效率**：减少网络请求次数，提高大文件传输速度
2. **减少服务器负载**：减少HTTP请求数量
3. **改善用户体验**：大文件上传下载更流畅

### 注意事项
1. **内存使用**：单个分片内存占用增加到20MB
2. **网络稳定性**：对网络稳定性要求略有提高
3. **错误恢复**：单个分片失败时重传的数据量增加

## 测试建议

1. **大文件测试**：测试100MB+文件的上传下载
2. **网络中断测试**：验证暂停恢复功能
3. **并发测试**：测试多文件同时传输
4. **内存监控**：观察内存使用情况
5. **错误处理**：测试网络异常时的重试机制

## 回滚方案

如需回滚到5MB分片大小，将以下值改回：
- 环境变量中的 `VITE_TUS_CHUNK_SIZE` 和 `VITE_DOWNLOAD_CHUNK_SIZE` 改为 `5242880`
- 代码中的默认值从 `20 * 1024 * 1024` 改回 `5 * 1024 * 1024`
