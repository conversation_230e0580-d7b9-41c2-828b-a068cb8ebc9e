# 上传解压缩控制功能增强 - 文件数量判断

## 概述

本次更新增强了 `UploadDialog.vue` 中的解压缩控制逻辑，新增了基于文件数量的判断条件。当上传文件数量小于智能打包阈值时，系统将强制设置为不解压缩，优化小批量文件上传的处理逻辑。

## 功能需求

### 新增判断条件
- 当上传的文件数量小于智能打包阈值（`VITE_SMART_PACKING_THRESHOLD`）时，强制设置 `is_need_extract = 0`（不解压缩）
- 这个判断优先于分类配置的 `is_not_unpack` 字段

### 逻辑优先级
1. **第一层判断**：文件数量是否小于智能打包阈值
   - 如果 `fileCount < smartPackingThreshold`，直接返回 0（不解压缩）
   - 忽略分类的 `is_not_unpack` 配置

2. **第二层判断**：文件数量大于等于阈值时，按分类配置决定
   - 如果 `fileCount >= smartPackingThreshold`，根据分类的 `is_not_unpack` 字段判断
   - 保持原有的分类配置逻辑

## 实现细节

### 代码修改

#### `src/components/Upload/UploadDialog.vue`

**新增导入**：
```typescript
import { config } from '@/config'
```

**修改 `isNeedExtract` 计算属性**：
```typescript
// 根据文件数量和分类配置确定是否需要解压缩
const isNeedExtract = computed((): number => {
  // 第一层判断：文件数量是否小于智能打包阈值
  const smartPackingThreshold = config.smartPacking.threshold
  const fileCount = files.value.length
  
  if (fileCount < smartPackingThreshold) {
    // 文件数量小于阈值，强制不解压缩，忽略分类配置
    return 0
  }
  
  // 第二层判断：文件数量大于等于阈值，根据分类配置决定
  if (!currentCategory.value) return 0 // 默认不需要解压缩
  // is_not_unpack = "Y" 表示不需要解压，对应 is_need_extract = 0
  // is_not_unpack = "N" 表示需要解压，对应 is_need_extract = 1
  // 如果字段不存在（undefined），默认不需要解压缩
  return currentCategory.value.is_not_unpack === "N" ? 1 : 0
})
```

### 技术实现

#### 数据来源
- **文件数量**：从组件的 `files.value.length` 获取当前上传文件数量
- **智能打包阈值**：从 `config.smartPacking.threshold` 读取配置值
- **分类配置**：从 `currentCategory.value.is_not_unpack` 获取分类的解压缩配置

#### 响应式更新
- 计算属性会在以下情况自动重新计算：
  - 文件列表变化（`files.value` 变化）
  - 分类切换（`currentCategory.value` 变化）
  - 配置更新（`config.smartPacking.threshold` 变化）

## 预期行为

### 场景分析

#### 场景1：小批量文件上传
- **条件**：文件数量 < 智能打包阈值（默认10个）
- **行为**：`is_need_extract = 0`（强制不解压缩）
- **原因**：小批量文件通常不需要解压缩处理，直接上传更高效

#### 场景2：大批量文件上传 + 分类配置为不解压
- **条件**：文件数量 ≥ 阈值 && `is_not_unpack = "Y"`
- **行为**：`is_need_extract = 0`（不解压缩）
- **原因**：遵循分类配置，不进行解压缩

#### 场景3：大批量文件上传 + 分类配置为解压
- **条件**：文件数量 ≥ 阈值 && `is_not_unpack = "N"`
- **行为**：`is_need_extract = 1`（需要解压缩）
- **原因**：遵循分类配置，进行自动解压缩

#### 场景4：大批量文件上传 + 无分类配置
- **条件**：文件数量 ≥ 阈值 && 分类不存在或无配置
- **行为**：`is_need_extract = 0`（默认不解压缩）
- **原因**：采用安全的默认行为

## 业务逻辑说明

### 设计理念
1. **性能优化**：小批量文件跳过解压缩处理，提高上传效率
2. **智能判断**：结合文件数量和分类配置，提供最合适的处理方式
3. **向后兼容**：保持原有分类配置逻辑，只在小批量场景下覆盖

### 阈值意义
- **智能打包阈值**：既是智能打包的触发条件，也是解压缩判断的分界线
- **统一标准**：使用相同的阈值保持功能的一致性
- **可配置性**：通过环境变量灵活调整阈值

## 测试建议

### 功能测试
1. **小批量文件测试**：
   - 上传1-9个文件，验证 `is_need_extract = 0`
   - 测试不同分类配置下的行为一致性

2. **阈值边界测试**：
   - 上传恰好等于阈值的文件数量，验证按分类配置处理
   - 上传超过阈值的文件数量，验证分类配置生效

3. **分类配置测试**：
   - 测试 `is_not_unpack = "Y"` 的分类
   - 测试 `is_not_unpack = "N"` 的分类
   - 测试无配置的分类

4. **动态更新测试**：
   - 在上传对话框中添加/删除文件，验证计算属性实时更新
   - 切换分类，验证解压缩配置正确更新

### 边界情况测试
1. **空文件列表**：验证0个文件时的行为
2. **分类不存在**：验证无效分类ID时的默认行为
3. **配置缺失**：验证环境变量未设置时的回退逻辑

## 影响范围

### 前端组件
- **`UploadDialog.vue`**：核心逻辑修改
- **上传流程**：`startTask` API调用时传递正确的 `is_need_extract` 参数

### 后端处理
- **文件上传**：根据 `is_need_extract` 参数决定是否进行自动解压缩
- **任务处理**：小批量文件跳过解压缩步骤，提高处理效率

### 用户体验
- **上传速度**：小批量文件上传更快
- **处理逻辑**：更智能的解压缩判断
- **一致性**：与智能打包功能保持逻辑一致

## 注意事项

1. **阈值同步**：确保智能打包阈值和解压缩判断使用相同的配置值
2. **默认行为**：在任何不确定的情况下，都采用不解压缩的安全默认值
3. **性能考虑**：计算属性的响应式更新不会影响组件性能
4. **向后兼容**：现有的分类配置功能完全保留，只在小批量场景下增强

## 配置依赖

此功能依赖以下配置：
- **环境变量**：`VITE_SMART_PACKING_THRESHOLD`（默认值：10）
- **配置对象**：`config.smartPacking.threshold`
- **分类数据**：`currentCategory.value.is_not_unpack`

确保这些配置正确设置，功能才能正常工作。
