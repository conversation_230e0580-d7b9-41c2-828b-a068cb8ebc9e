# 文件上传解压缩控制功能实现

## 概述

本次更新实现了基于分类配置的文件上传解压缩控制功能。通过 `api.common.getTopCategoryDirectories` 接口返回的 `is_not_unpack` 字段，前端可以在调用 `startTask` 接口时动态设置 `is_need_extract` 参数，从而控制上传文件是否需要自动解压缩。

## 功能需求

### API响应字段更新
- `api.common.getTopCategoryDirectories` 接口返回值新增字段 `is_not_unpack`
- 字段值：`"Y"` = 不需要自动解压缩，`"N"` = 需要自动解压缩

### API调用参数更新
- `startTask` 接口新增参数 `is_need_extract`
- 参数值：`1` = 需要解压缩，`0` = 不需要解压缩

### 字段映射关系
- `is_not_unpack = "Y"` → `is_need_extract = 0`（不需要解压）
- `is_not_unpack = "N"` → `is_need_extract = 1`（需要解压）

## 实现细节

### 1. 类型定义更新

#### `src/store/sidebar.ts`
```typescript
// 接口返回的数据类型
export interface ApiCategoryItem {
  id: number;
  name: string;
  category_id: number;
  category_key: string;
  is_not_unpack?: "Y" | "N"; // 新增字段（可选，向后兼容）
}

// 合并后的侧边栏项类型
export interface SidebarItem {
  id: number;
  name: string;
  category_id: number;
  category_key: string;
  path: string;
  icon: any;
  iconColor?: string;
  description?: string;
  is_not_unpack?: "Y" | "N"; // 新增字段（可选，向后兼容）
}
```

#### `src/api/services/files.ts`
```typescript
interface IUploadTaskData {
  upload_url: string;
  relative_path: string;
  category_id: number | string;
  parent_id: number | string;
  is_folder?: number;
  is_need_extract?: number; // 新增字段：是否需要解压缩，1=需要，0=不需要
  [key: string]: any;
}
```

### 2. 业务逻辑实现

#### `src/components/Upload/UploadDialog.vue`

**导入依赖**：
```typescript
import { useSidebarStore } from '@/store/sidebar'
```

**获取分类信息**：
```typescript
const sidebarStore = useSidebarStore()

// 获取当前分类信息
const currentCategory = computed(() => {
  if (!categoryId.value) return null
  return sidebarStore.sidebarItems.find(item => item.category_id === Number(categoryId.value))
})
```

**解压缩控制逻辑**：
```typescript
// 根据分类配置确定是否需要解压缩
const isNeedExtract = computed((): number => {
  if (!currentCategory.value) return 0 // 默认不需要解压缩
  // is_not_unpack = "Y" 表示不需要解压，对应 is_need_extract = 0
  // is_not_unpack = "N" 表示需要解压，对应 is_need_extract = 1
  // 如果字段不存在（undefined），默认不需要解压缩
  return currentCategory.value.is_not_unpack === "N" ? 1 : 0
})
```

**API调用更新**：
```typescript
const startTaskData = {
  upload_url: task.uploadUrl,
  category_id: categoryId.value,
  parent_id: parentId.value,
  relative_path: task.metadata?.relativePath || file.name,
  is_folder: task.isFolder ? 1 : 0,
  is_need_extract: isNeedExtract.value, // 新增参数
  ...userAttributes
}

const response = await filesApi.startTask(startTaskData)
```

## 向后兼容性

### 字段可选性
- `is_not_unpack` 字段设计为可选字段（`?:`），确保现有API响应不会因缺少该字段而出错
- 当字段不存在时，默认行为为不需要解压缩（`is_need_extract = 0`）

### 默认行为
- 如果分类信息不存在：`is_need_extract = 0`
- 如果 `is_not_unpack` 字段不存在：`is_need_extract = 0`
- 如果 `is_not_unpack = "Y"`：`is_need_extract = 0`
- 如果 `is_not_unpack = "N"`：`is_need_extract = 1`

## 测试建议

### 功能测试
1. **分类配置测试**：
   - 测试 `is_not_unpack = "Y"` 的分类，验证上传文件不会自动解压
   - 测试 `is_not_unpack = "N"` 的分类，验证上传文件会自动解压

2. **兼容性测试**：
   - 测试没有 `is_not_unpack` 字段的API响应
   - 验证默认行为（不解压）是否正常工作

3. **边界情况测试**：
   - 测试分类ID不存在的情况
   - 测试网络异常时的处理

### API测试
1. **请求参数验证**：
   - 验证 `startTask` 接口正确接收 `is_need_extract` 参数
   - 验证参数值的正确性（0或1）

2. **响应处理验证**：
   - 验证后端正确处理解压缩控制逻辑
   - 验证文件上传后的解压缩行为

## 影响范围

### 前端修改
- `src/store/sidebar.ts`：类型定义更新
- `src/api/services/files.ts`：接口参数类型更新
- `src/components/Upload/UploadDialog.vue`：业务逻辑实现

### 后端要求
- `getTopCategoryDirectories` 接口需要返回 `is_not_unpack` 字段
- `startTask` 接口需要支持 `is_need_extract` 参数
- 需要实现基于该参数的解压缩控制逻辑

## 注意事项

1. **字段命名**：
   - 前端使用 `is_not_unpack`（否定逻辑）
   - API参数使用 `is_need_extract`（肯定逻辑）
   - 注意逻辑转换的正确性

2. **默认行为**：
   - 系统默认为不解压缩，这是更安全的选择
   - 只有明确配置需要解压缩的分类才会自动解压

3. **性能考虑**：
   - 分类信息通过 sidebar store 缓存，避免重复请求
   - 计算属性确保响应式更新

4. **错误处理**：
   - 当分类信息获取失败时，采用安全的默认行为
   - 确保上传流程不会因为解压缩配置问题而中断
