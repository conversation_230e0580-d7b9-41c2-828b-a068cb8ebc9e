# 空文件夹创建逻辑重构

## 概述

本次重构消除了 `useDragAndDrop.ts` 和 `FileUploadArea.vue` 中空文件夹 File 对象创建逻辑的代码重复。

## 重构内容

### 1. 创建工具函数文件

**文件**: `src/utils/fileUtils.ts`

新增了以下工具函数：

- `createEmptyFolderFile(fileInfo: FileInfo): File` - 创建空文件夹的 File 对象
- `isEmptyFolder(fileInfo: FileInfo): boolean` - 检查是否为空文件夹
- `extractFileName(filePath: string): string` - 从路径提取文件名（跨平台）
- `hasPathStructure(relativePath?: string, fileName?: string): boolean` - 检查是否包含目录结构

### 2. 更新的文件

#### `src/composables/useDragAndDrop.ts`
- 导入工具函数：`createEmptyFolderFile`, `isEmptyFolder`
- 替换原有的 40+ 行空文件夹创建逻辑为 2 行函数调用

**修改前**:
```typescript
// 检查是否为空文件夹
if (fileInfo.isDirectory && fileInfo.isEmpty) {
  // 为空文件夹创建特殊的 File 对象
  const folderName = fileInfo.name || fileInfo.path.split(/[/\\]/).pop() || "unknown";
  const file = new File([], folderName, {
    type: "application/x-directory",
    lastModified: Date.now(),
  });
  // ... 大量属性设置代码
}
```

**修改后**:
```typescript
// 检查是否为空文件夹
if (isEmptyFolder(fileInfo)) {
  // 使用工具函数创建空文件夹 File 对象
  const file = createEmptyFolderFile(fileInfo);
}
```

#### `src/components/Upload/FileUploadArea.vue`
- 导入工具函数：`createEmptyFolderFile`, `isEmptyFolder`
- 同样替换原有的空文件夹创建逻辑

### 3. 新增测试文件

**文件**: `src/utils/__tests__/fileUtils.test.ts`

包含完整的单元测试，覆盖：
- 空文件夹识别逻辑
- File 对象创建逻辑
- 跨平台路径处理
- 边界情况处理

## 优化效果

### 代码减少
- 总共减少了约 80 行重复代码
- 提高了代码的可维护性和可测试性

### 功能保持
- 所有原有功能保持不变
- 跨平台路径兼容性保持
- 错误处理方式保持一致

### 可扩展性
- 工具函数可以在其他需要处理空文件夹的地方复用
- 统一的文件处理逻辑便于后续维护

## 使用示例

```typescript
import { createEmptyFolderFile, isEmptyFolder } from '@/utils/fileUtils';

// 检查是否为空文件夹
if (isEmptyFolder(fileInfo)) {
  // 创建空文件夹 File 对象
  const file = createEmptyFolderFile(fileInfo);
  // 使用 file 对象...
}
```

## 注意事项

1. **跨平台兼容性**: 工具函数支持 Windows (`\`) 和 Unix (`/`) 路径分隔符
2. **属性设置**: 创建的 File 对象包含所有必要的自定义属性（path, isFolder, isEmpty 等）
3. **类型安全**: 使用 TypeScript 接口确保类型安全
4. **测试覆盖**: 包含完整的单元测试确保功能正确性

## 后续建议

1. 考虑将 `createFileFromPath` 函数也提取到工具文件中
2. 可以考虑创建更多文件处理相关的工具函数
3. 在其他需要处理文件对象的地方使用这些工具函数
